# Terminal Shell Integration 修复测试

## 问题描述
终端的shell integration可能有问题，导致降级出现了sendText，而不是直接executeCommand()。

## 修复内容

### 1. TerminalProcess.ts 修复
- 移除了不必要的 `resetInternalState()` 调用
- 简化了 Shell Integration 状态检查
- 移除了过度的日志输出
- 清理了未使用的方法

### 2. TerminalManager.ts 修复
- 移除了复杂的 CWD 切换逻辑
- 添加了 `executeCommandWithCwdCheck` 方法来智能处理目录切换
- 修改了 `runCommand` 方法签名，添加了可选的 `targetCwd` 参数
- 简化了终端重用逻辑

### 3. 核心修复逻辑
当需要在不同目录执行命令时，不再预先执行 `cd` 命令，而是：
1. 检查当前终端的 CWD 是否匹配目标目录
2. 如果匹配，直接执行命令
3. 如果不匹配，构建 `cd "目标目录" && 原命令` 的组合命令

## 测试步骤
1. 在不同目录下执行命令
2. 观察终端输出，确认没有多余的 `cd` 命令
3. 确认 Shell Integration 正常工作
4. 验证命令在正确的目录下执行

## 预期结果
- 不再出现错误的 `cd` 命令
- Shell Integration 保持稳定
- 命令执行更加流畅
- 减少不必要的状态重置
